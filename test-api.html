<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Remove.bg API Test</h1>
    
    <button onclick="testConfigStatus()">Test Config Status</button>
    <button onclick="testImageUpload()">Test Image Upload</button>
    
    <div id="results"></div>

    <script>
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
        }

        async function testConfigStatus() {
            try {
                const response = await fetch('/api/config/status');
                const data = await response.json();
                addResult(`Config Status: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                addResult(`Config Status Error: ${error.message}`, false);
            }
        }

        async function testImageUpload() {
            try {
                // Create a simple test image (1x1 pixel PNG)
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = 'red';
                ctx.fillRect(0, 0, 1, 1);
                
                canvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('image', blob, 'test.png');
                    
                    const response = await fetch('/api/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    addResult(`Upload Response: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.id) {
                        // Poll for status
                        pollStatus(data.id);
                    }
                }, 'image/png');
            } catch (error) {
                addResult(`Upload Error: ${error.message}`, false);
            }
        }

        async function pollStatus(id) {
            let attempts = 0;
            const maxAttempts = 10;
            
            const poll = async () => {
                try {
                    const response = await fetch(`/api/process/${id}`);
                    const data = await response.json();
                    addResult(`Status Check ${attempts + 1}: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.status === 'completed' || data.status === 'failed' || attempts >= maxAttempts) {
                        return;
                    }
                    
                    attempts++;
                    setTimeout(poll, 1000);
                } catch (error) {
                    addResult(`Status Check Error: ${error.message}`, false);
                }
            };
            
            poll();
        }
    </script>
</body>
</html>
