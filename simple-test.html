<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-area {
            border: 2px solid #333;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            background: #f9f9f9;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            border-radius: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #output {
            background: #000;
            color: #0f0;
            padding: 15px;
            font-family: monospace;
            margin-top: 20px;
            min-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Simple Upload Test</h1>
    
    <div class="test-area">
        <h3>Step 1: Test Basic JavaScript</h3>
        <button onclick="testJS()">Test JavaScript</button>
        <button onclick="clearOutput()">Clear</button>
    </div>
    
    <div class="test-area">
        <h3>Step 2: Test File Input</h3>
        <button onclick="triggerFileInput()">Open File Dialog</button>
        <input type="file" id="fileInput" style="display: none;" onchange="handleFileSelect(event)">
    </div>
    
    <div class="test-area">
        <h3>Step 3: Test API Call</h3>
        <button onclick="testAPI()">Test API</button>
    </div>
    
    <div id="output"></div>

    <script>
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
            console.clear();
            log('Output cleared');
        }

        function testJS() {
            log('JavaScript is working!');
            log('Browser: ' + navigator.userAgent);
            log('URL: ' + window.location.href);
        }

        function triggerFileInput() {
            log('Attempting to open file dialog...');
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.click();
                log('File input click() called');
            } else {
                log('ERROR: File input not found');
            }
        }

        function handleFileSelect(event) {
            log('File input change event triggered');
            const files = event.target.files;
            log(`Files selected: ${files.length}`);
            
            if (files.length > 0) {
                const file = files[0];
                log(`File: ${file.name}, Type: ${file.type}, Size: ${file.size} bytes`);
                
                // Test upload
                uploadFile(file);
            }
        }

        async function uploadFile(file) {
            log('Starting file upload...');
            
            try {
                const formData = new FormData();
                formData.append('image', file);
                
                log('Sending POST request to /api/upload');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                log(`Response status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log(`Upload success: ${JSON.stringify(result)}`);
                } else {
                    const errorText = await response.text();
                    log(`Upload failed: ${errorText}`);
                }
            } catch (error) {
                log(`Upload error: ${error.message}`);
            }
        }

        async function testAPI() {
            log('Testing API connection...');
            
            try {
                log('Calling /api/config/status');
                const response = await fetch('/api/config/status');
                log(`Status response: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log(`API result: ${JSON.stringify(result)}`);
                } else {
                    log(`API error: ${response.status}`);
                }
            } catch (error) {
                log(`API test error: ${error.message}`);
            }
        }

        // Initialize
        window.onload = function() {
            log('Page loaded successfully');
            log('Ready for testing');
        };
    </script>
</body>
</html>
