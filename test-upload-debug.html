<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #999;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #5855eb;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        #log {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Upload Debug Test</h1>
    
    <div style="margin-bottom: 20px;">
        <button id="testBtn" style="background: #28a745;">测试JavaScript</button>
        <button id="clearBtn" style="background: #dc3545; margin-left: 10px;">清空日志</button>
    </div>

    <div class="upload-area" id="uploadArea">
        <h3>Click to Upload Image</h3>
        <p>或拖拽图片到这里</p>
        <button id="uploadBtn">选择图片</button>
        <input type="file" id="fileInput" accept="image/*" style="display: none;">
    </div>
    
    <div id="log"></div>

    <script>
        const log = document.getElementById('log');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadArea = document.getElementById('uploadArea');

        function addLog(message) {
            try {
                const timestamp = new Date().toLocaleTimeString();
                if (log) {
                    log.textContent += `[${timestamp}] ${message}\n`;
                    log.scrollTop = log.scrollHeight;
                }
                console.log(message);
            } catch (error) {
                console.error('addLog error:', error);
            }
        }

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            addLog(`JavaScript错误: ${e.error.message}`);
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled Promise Rejection:', e.reason);
            addLog(`Promise错误: ${e.reason}`);
        });

        addLog('页面加载完成');
        addLog(`当前URL: ${window.location.href}`);
        addLog(`User Agent: ${navigator.userAgent}`);

        // 测试按钮
        const testBtn = document.getElementById('testBtn');
        const clearBtn = document.getElementById('clearBtn');

        if (testBtn) {
            testBtn.addEventListener('click', () => {
                addLog('测试按钮被点击 - JavaScript正常工作!');
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                if (log) {
                    log.textContent = '';
                }
                console.clear();
                addLog('日志已清空');
            });
        }

        // 检查元素是否存在
        if (!uploadArea) {
            addLog('错误: uploadArea 元素未找到');
        }
        if (!uploadBtn) {
            addLog('错误: uploadBtn 元素未找到');
        }
        if (!fileInput) {
            addLog('错误: fileInput 元素未找到');
        }

        // 点击上传区域
        if (uploadArea) {
            uploadArea.addEventListener('click', (e) => {
                try {
                    addLog(`上传区域被点击, target: ${e.target.tagName}, id: ${e.target.id}`);
                    if (e.target !== uploadBtn) {
                        addLog('触发文件输入框点击');
                        if (fileInput) {
                            fileInput.click();
                        } else {
                            addLog('错误: fileInput 不存在');
                        }
                    }
                } catch (error) {
                    addLog(`上传区域点击错误: ${error.message}`);
                }
            });
        }

        // 点击按钮
        if (uploadBtn) {
            uploadBtn.addEventListener('click', (e) => {
                try {
                    e.stopPropagation();
                    addLog('上传按钮被点击');
                    if (fileInput) {
                        fileInput.click();
                    } else {
                        addLog('错误: fileInput 不存在');
                    }
                } catch (error) {
                    addLog(`上传按钮点击错误: ${error.message}`);
                }
            });
        }

        // 文件选择
        fileInput.addEventListener('change', async (e) => {
            addLog('文件输入框 change 事件触发');
            const files = e.target.files;
            addLog(`选择了 ${files.length} 个文件`);
            
            if (files.length > 0) {
                const file = files[0];
                addLog(`文件信息: ${file.name}, ${file.type}, ${file.size} bytes`);
                
                // 测试上传
                await uploadFile(file);
            }
            
            // 重置输入框
            e.target.value = '';
        });

        // 拖拽处理
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#6366f1';
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
        });

        uploadArea.addEventListener('drop', async (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            addLog('文件被拖拽到上传区域');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                addLog(`拖拽文件信息: ${file.name}, ${file.type}, ${file.size} bytes`);
                await uploadFile(file);
            }
        });

        async function uploadFile(file) {
            addLog('开始上传文件...');
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            
            try {
                const formData = new FormData();
                formData.append('image', file);
                
                addLog('发送 POST 请求到 /api/upload');
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });
                
                addLog(`响应状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const result = await response.json();
                    addLog(`上传成功: ${JSON.stringify(result)}`);
                } else {
                    const errorText = await response.text();
                    addLog(`上传失败: ${errorText}`);
                }
            } catch (error) {
                addLog(`上传错误: ${error.message}`);
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '选择图片';
            }
        }
    </script>
</body>
</html>
