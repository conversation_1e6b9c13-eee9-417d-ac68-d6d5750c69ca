<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>Remove Background from Image - 100% Free & AI-Powered | Remove bg</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon-16x16.svg" />
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon-32x32.svg" />
    <link rel="shortcut icon" href="/favicon.svg" />

    <!-- SEO Meta Tags -->
    <meta name="description" content="Remove image backgrounds automatically with AI. Upload your photo and get a transparent PNG in 5 seconds. Works with people, products, animals, cars, and graphics.">
    <meta name="keywords" content="remove background, transparent background, PNG, AI background removal, photo editing, image editor, background remover">
    <meta name="robots" content="index, follow">
    <meta name="author" content="Remove bg">
    <meta name="theme-color" content="#3b82f6">
    <meta name="msapplication-TileColor" content="#3b82f6">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Remove Background from Image - 100% Free & AI-Powered">
    <meta property="og:description" content="Remove image backgrounds automatically with AI. Upload your photo and get a transparent PNG in 5 seconds.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.remove.bg">
    <meta property="og:image" content="https://www.remove.bg/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_US">
    <meta property="og:site_name" content="Remove bg">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Remove Background from Image - 100% Free & AI-Powered">
    <meta name="twitter:description" content="Remove image backgrounds automatically with AI. Upload your photo and get a transparent PNG in 5 seconds.">
    <meta name="twitter:image" content="https://www.remove.bg/og-image.jpg">
    <meta name="twitter:site" content="@removebg">
    <meta name="twitter:creator" content="@removebg">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Remove bg",
        "description": "AI-powered background removal tool that automatically removes image backgrounds with precision",
        "url": "https://www.remove.bg",
        "applicationCategory": "PhotoEditingApplication",
        "operatingSystem": "Web Browser",
        "inLanguage": "en",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "creator": {
            "@type": "Organization",
            "name": "Remove bg",
            "url": "https://www.remove.bg"
        },
        "featureList": [
            "AI-powered background removal",
            "Transparent PNG output",
            "Batch processing",
            "API integration",
            "Multiple image formats support"
        ],
        "screenshot": "https://www.remove.bg/screenshot.jpg"
    }
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GG4JZGVKF8"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-GG4JZGVKF8');
    </script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
