import { type Language } from './i18n';

interface SitemapUrl {
  loc: string;
  lastmod: string;
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
  alternates?: Array<{ hreflang: string; href: string }>;
}

export const generateSitemapUrls = (): SitemapUrl[] => {
  const baseUrl = "https://www.remove.bg";
  const languages: Language[] = ['en', 'zh', 'es', 'fr', 'de'];
  const currentDate = new Date().toISOString().split('T')[0];
  
  const routes = [
    { path: '/', priority: 1.0, changefreq: 'daily' as const },
    { path: '/help', priority: 0.8, changefreq: 'weekly' as const },
    { path: '/contact', priority: 0.7, changefreq: 'monthly' as const },
    { path: '/status', priority: 0.6, changefreq: 'weekly' as const },
    { path: '/feedback', priority: 0.6, changefreq: 'monthly' as const },
    { path: '/privacy', priority: 0.6, changefreq: 'monthly' as const },
    { path: '/terms', priority: 0.6, changefreq: 'monthly' as const },
    { path: '/cookies', priority: 0.5, changefreq: 'monthly' as const },
    { path: '/imprint', priority: 0.5, changefreq: 'monthly' as const },
    { path: '/tools', priority: 0.8, changefreq: 'weekly' as const },
    { path: '/blog', priority: 0.7, changefreq: 'weekly' as const },
  ];

  const urls: SitemapUrl[] = [];

  routes.forEach(route => {
    // Generate alternates for each route
    const alternates = languages.map(lang => ({
      hreflang: lang,
      href: lang === 'en' ? `${baseUrl}${route.path}` : `${baseUrl}/${lang}${route.path}`
    }));
    
    // Add x-default
    alternates.push({
      hreflang: 'x-default',
      href: `${baseUrl}${route.path}`
    });

    // Add URL for each language
    languages.forEach(lang => {
      const loc = lang === 'en' ? `${baseUrl}${route.path}` : `${baseUrl}/${lang}${route.path}`;
      
      urls.push({
        loc,
        lastmod: currentDate,
        changefreq: route.changefreq,
        priority: route.priority,
        alternates
      });
    });
  });

  return urls;
};

export const generateSitemapXML = (): string => {
  const urls = generateSitemapUrls();
  
  let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
`;

  urls.forEach(url => {
    xml += `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
`;

    if (url.alternates) {
      url.alternates.forEach(alternate => {
        xml += `    <xhtml:link rel="alternate" hreflang="${alternate.hreflang}" href="${alternate.href}" />
`;
      });
    }

    xml += `  </url>
`;
  });

  xml += `</urlset>`;
  
  return xml;
};

// Function to save sitemap to public directory (for build process)
export const saveSitemap = async (): Promise<void> => {
  const sitemapXML = generateSitemapXML();
  
  // This would typically be used in a build script
  // For now, we'll just log it or you can manually copy it
  console.log('Generated sitemap.xml:');
  console.log(sitemapXML);
  
  // In a real build process, you would write this to the public directory:
  // await fs.writeFile('client/public/sitemap.xml', sitemapXML);
};
