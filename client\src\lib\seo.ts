import { type Language } from './i18n';

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string;
  canonical?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
  jsonLd?: object;
}

export const defaultSEOConfig: Record<Language, SEOConfig> = {
  en: {
    title: "Remove Background from Image - 100% Free & AI-Powered | Remove bg",
    description: "Remove image backgrounds automatically with AI. Upload your photo and get a transparent PNG in 5 seconds. Works with people, products, animals, cars, and graphics.",
    keywords: "remove background, transparent background, PNG, AI background removal, photo editing, image editor, background remover",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
  zh: {
    title: "移除图片背景 - 100% 免费AI智能抠图 | Remove bg",
    description: "使用AI自动移除图片背景。上传您的照片，5秒内获得透明PNG。支持人物、产品、动物、汽车和图形。",
    keywords: "移除背景, 透明背景, PNG, AI背景移除, 照片编辑, 图片编辑器, 背景移除器, 抠图",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
  es: {
    title: "Eliminar Fondo de Imagen - 100% Gratis e IA | Remove bg",
    description: "Elimina fondos de imágenes automáticamente con IA. Sube tu foto y obtén un PNG transparente en 5 segundos. Funciona con personas, productos, animales, autos y gráficos.",
    keywords: "eliminar fondo, fondo transparente, PNG, eliminación de fondo IA, edición de fotos, editor de imágenes",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
  fr: {
    title: "Supprimer l'Arrière-plan d'Image - 100% Gratuit et IA | Remove bg",
    description: "Supprimez automatiquement les arrière-plans d'images avec l'IA. Téléchargez votre photo et obtenez un PNG transparent en 5 secondes. Fonctionne avec les personnes, produits, animaux, voitures et graphiques.",
    keywords: "supprimer arrière-plan, arrière-plan transparent, PNG, suppression arrière-plan IA, édition photo, éditeur d'images",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
  de: {
    title: "Bildhintergrund Entfernen - 100% Kostenlos & KI-Gestützt | Remove bg",
    description: "Entfernen Sie Bildhintergründe automatisch mit KI. Laden Sie Ihr Foto hoch und erhalten Sie in 5 Sekunden ein transparentes PNG. Funktioniert mit Personen, Produkten, Tieren, Autos und Grafiken.",
    keywords: "Hintergrund entfernen, transparenter Hintergrund, PNG, KI Hintergrundentfernung, Fotobearbeitung, Bildbearbeiter",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
};

export const generateHreflangLinks = (currentPath: string): Array<{ hreflang: string; href: string }> => {
  const baseUrl = "https://www.remove.bg";
  const languages: Language[] = ['en', 'zh', 'es', 'fr', 'de'];
  
  const links: Array<{ hreflang: string; href: string }> = [];
  
  // Remove language prefix from current path
  const pathWithoutLang = currentPath.replace(/^\/(en|zh|es|fr|de)/, '') || '/';
  
  // Add hreflang for each language
  languages.forEach(lang => {
    const href = lang === 'en' 
      ? `${baseUrl}${pathWithoutLang}`
      : `${baseUrl}/${lang}${pathWithoutLang}`;
    
    links.push({
      hreflang: lang,
      href: href
    });
  });
  
  // Add x-default (points to English version)
  links.push({
    hreflang: 'x-default',
    href: `${baseUrl}${pathWithoutLang}`
  });
  
  return links;
};

export const generateCanonicalUrl = (currentPath: string, language: Language): string => {
  const baseUrl = "https://www.remove.bg";
  const pathWithoutLang = currentPath.replace(/^\/(en|zh|es|fr|de)/, '') || '/';
  
  return language === 'en' 
    ? `${baseUrl}${pathWithoutLang}`
    : `${baseUrl}/${language}${pathWithoutLang}`;
};

export const generateJsonLd = (language: Language, currentPath: string) => {
  const baseUrl = "https://www.remove.bg";
  const config = defaultSEOConfig[language];
  
  const baseJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Remove bg",
    "description": config.description,
    "url": generateCanonicalUrl(currentPath, language),
    "applicationCategory": "PhotoEditingApplication",
    "operatingSystem": "Web Browser",
    "inLanguage": language,
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "creator": {
      "@type": "Organization",
      "name": "Remove bg",
      "url": baseUrl
    }
  };

  // Add breadcrumb for non-homepage
  if (currentPath !== '/' && !currentPath.match(/^\/(en|zh|es|fr|de)\/?$/)) {
    return {
      ...baseJsonLd,
      "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": generateCanonicalUrl('/', language)
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": config.title,
            "item": generateCanonicalUrl(currentPath, language)
          }
        ]
      }
    };
  }

  return baseJsonLd;
};
